package com.baosight.znyy.common.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * JSON工具类
 * <p>
 * 提供JSON序列化和反序列化的工具方法。
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
public class JsonUtil {

    /**
     * JSON对象映射器
     */
    @Getter
    private static ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 更新ObjectMapper
     *
     * @param newObjectMapper 新的ObjectMapper
     */
    public static void updateObjectMapper(ObjectMapper newObjectMapper) {
        if (newObjectMapper != null) {
            objectMapper = newObjectMapper;
            log.info("ObjectMapper updated");
        }
    }

    private JsonUtil() {
        // 私有构造函数，防止实例化
    }

    /**
     * 将对象转换为JSON字符串
     *
     * @param obj 要转换的对象
     * @return JSON字符串，如果转换失败则返回null
     */
    public static String toJson(Object obj) {
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.error("Error converting object to JSON", e);
            return null;
        }
    }

    /**
     * 将JSON字符串转换为对象
     *
     * @param <T>   目标类型
     * @param json  JSON字符串
     * @param clazz 目标类
     * @return 转换后的对象，如果转换失败则返回null
     */
    public static <T> T fromJson(String json, Class<T> clazz) {
        try {
            return objectMapper.readValue(json, clazz);
        } catch (IOException e) {
            log.error("Error converting JSON to object", e);
            return null;
        }
    }

    /**
     * 将JSON字符串转换为List
     *
     * @param <T>          列表元素类型
     * @param json         JSON字符串
     * @param elementClass 列表元素类
     * @return 转换后的List，如果转换失败则返回null
     */
    public static <T> List<T> fromJsonToList(String json, Class<T> elementClass) {
        try {
            return objectMapper.readValue(json, objectMapper.getTypeFactory().constructCollectionType(List.class, elementClass));
        } catch (IOException e) {
            log.error("Error converting JSON to list", e);
            return null;
        }
    }

    /**
     * 将JSON字符串转换为Map
     *
     * @param <K>        键类型
     * @param <V>        值类型
     * @param json       JSON字符串
     * @param keyClass   键类
     * @param valueClass 值类
     * @return 转换后的Map，如果转换失败则返回null
     */
    public static <K, V> Map<K, V> fromJsonToMap(String json, Class<K> keyClass, Class<V> valueClass) {
        try {
            return objectMapper.readValue(json, objectMapper.getTypeFactory().constructMapType(Map.class, keyClass, valueClass));
        } catch (IOException e) {
            log.error("Error converting JSON to map", e);
            return null;
        }
    }

    /**
     * 将JSON字符串转换为复杂类型
     *
     * @param <T>           目标类型
     * @param json          JSON字符串
     * @param typeReference 类型引用
     * @return 转换后的对象，如果转换失败则返回null
     */
    public static <T> T fromJson(String json, TypeReference<T> typeReference) {
        try {
            return objectMapper.readValue(json, typeReference);
        } catch (IOException e) {
            log.error("Error converting JSON to complex type", e);
            return null;
        }
    }

    /**
     * 将JSON字符串转换为JsonNode
     *
     * @param json JSON字符串
     * @return JsonNode对象，如果转换失败则返回null
     */
    public static JsonNode toJsonNode(String json) {
        try {
            return objectMapper.readTree(json);
        } catch (IOException e) {
            log.error("Error converting JSON to JsonNode", e);
            return null;
        }
    }

    /**
     * 从文件加载JSON
     *
     * @param <T>      目标类型
     * @param file     JSON文件
     * @param valueType 目标类型的Class对象
     * @return 转换后的对象，如果转换失败则返回null
     */
    public static <T> T loadFromFile(File file, Class<T> valueType) {
        try {
            return objectMapper.readValue(file, valueType);
        } catch (IOException e) {
            log.error("Error loading JSON from file: {}", file.getPath(), e);
            return null;
        }
    }

    /**
     * 从文件加载JSON为复杂类型
     *
     * @param <T>           目标类型
     * @param file          JSON文件
     * @param typeReference 类型引用
     * @return 转换后的对象，如果转换失败则返回null
     */
    public static <T> T loadFromFile(File file, TypeReference<T> typeReference) {
        try {
            return objectMapper.readValue(file, typeReference);
        } catch (IOException e) {
            log.error("Error loading JSON from file: {}", file.getPath(), e);
            return null;
        }
    }

    /**
     * 将对象保存为JSON文件
     *
     * @param file   目标文件
     * @param object 要保存的对象
     * @return 是否保存成功
     */
    public static boolean saveToFile(File file, Object object) {
        try {
            objectMapper.writeValue(file, object);
            return true;
        } catch (IOException e) {
            log.error("Error saving JSON to file: {}", file.getPath(), e);
            return false;
        }
    }

    /**
     * 将对象保存为格式化的JSON文件
     *
     * @param file   目标文件
     * @param object 要保存的对象
     * @return 是否保存成功
     */
    public static boolean savePrettyToFile(File file, Object object) {
        try {
            objectMapper.writerWithDefaultPrettyPrinter().writeValue(file, object);
            return true;
        } catch (IOException e) {
            log.error("Error saving pretty JSON to file: {}", file.getPath(), e);
            return false;
        }
    }
}
