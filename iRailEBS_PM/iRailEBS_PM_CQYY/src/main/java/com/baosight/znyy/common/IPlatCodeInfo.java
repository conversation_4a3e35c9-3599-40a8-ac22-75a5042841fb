package com.baosight.znyy.common;

import java.util.List;
import java.util.Map;

/**
 * 获取平台小代码相关信息
 */
public interface IPlatCodeInfo {

    /**
     * 根据代码分类编号，代码项编号和状态获取小代码的英文名
     * @param typeCode 代码分类编号
     * @param code 代码项编号
     * @param type 状态
     * @return
     */
    public String getCodeEName(String typeCode,String code,String type) throws Exception;

    /**
     * 根据代码分类编号，代码项编号和状态获取小代码的中文名
     * @param typeCode 代码分类编号
     * @param code 代码项编号
     * @param type 状态
     * @return
     */
    public String getCodeCName(String typeCode,String code,String type) throws Exception;

    /**
     * 根据代码分类编号，代码项编号和状态获取小代码的所有信息
     * @param typeCode 代码分类编号
     * @param code 代码项编号
     * @param type 状态
     * @return
     */
    public List<Map> getCode(String typeCode, String code, String type);

    /**
     * 根据代码分类编号，代码项编号获取小代码的所有信息
     * @param typeCode 代码分类编号
     * @param code 代码项编号
     * @return
     */
    public List<Map> getCode(String typeCode,String code);

    /**
     * 根据代码分类编号获取小代码的所有信息
     * @param typeCode 代码分类编号
     * @return
     */
    public List<Map> getCode(String typeCode);

    /**
     * 根据代码分类编号获取小代码的名称和编码
     * @param typeCode 代码分类编号
     * @return
     */
    public List<Map<String, String>> getCodeAndName(String typeCode);
    public List<Map<String, String>> getCodeAndName_page(String typeCode,int offset,int limit);
    public int getCodeAndName_count(String typeCode);

    /**
     * 根据小代码中文名获取小代码
     * @param codeCNAME
     * @return
     */
    List getCodeByCodeCName(String codeCNAME, String typeCode);
    /**
     * 根据小代码英文名获取小代码
     * @param codeENAME
     * @return
     */
    List getCodeByCodeEName(String codeENAME,String typeCode);



}
