package com.baosight.znyy.common.impl;

import com.baosight.iplat4j.core.data.ibatis.dao.Dao;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;
import com.baosight.znyy.common.IPlatCodeInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Supplier;

/**
 * @description：获取平台小代码相关信息
 * @author：yanghuanbo
 * @date：2025/06/04
 */

@Service
public class PlatCodeInfoImpl implements IPlatCodeInfo {

    private static final Logger logger = LoggerFactory.getLogger(PlatCodeInfoImpl.class);
    private Dao dao = (Dao) PlatApplicationContext.getApplicationContext().getBean("dao");

    // 构建通用参数 Map
    private Map<String, String> buildParams(String typeCode, String code, String status) {
        Map<String, String> params = new HashMap<>();
        if (StringUtils.isNotBlank(typeCode)) params.put("codesetCode", typeCode);
        if (StringUtils.isNotBlank(code)) params.put("itemCode", code);
        if (StringUtils.isNotBlank(status)) params.put("status", status);
        return params;
    }

    // 公共异常处理封装
    private <T> T handleGet(Supplier<T> supplier, String errorMsg) {
        try {
            return supplier.get();
        } catch (Exception e) {
            logger.error(errorMsg, e);
            throw new RuntimeException(e);
        }
    }

    // 公共查询封装（带 Optional 空值处理）
    private Optional<Map> querySingleResult(String typeCode, String code, String type) {
        List<Map> result = this.getCode(typeCode, code, type);
        return Optional.ofNullable(result)
                .filter(list -> !list.isEmpty())
                .map(list -> list.get(0));
    }

    @Override
    public String getCodeEName(String typeCode, String code, String type) throws Exception {
        return handleGet(() -> {
                    try {
                        return querySingleResult(typeCode, code, type)
                                        .map(map -> (String) map.get("itemEname"))
                                        .orElseThrow(() -> new Exception("未找到对应的英文名"));
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                },
                "获取英文名失败，typeCode=" + typeCode + ", code=" + code + ", type=" + type);
    }

    @Override
    public String getCodeCName(String typeCode, String code, String type)  {
        return handleGet(() -> {
                    try {
                        return querySingleResult(typeCode, code, type)
                                        .map(map -> (String) map.get("itemCname"))
                                        .orElseThrow(() -> new Exception("未找到对应的中文名"));
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                },
                "获取中文名失败，typeCode=" + typeCode + ", code=" + code + ", type=" + type);
    }

    @Override
    public List<Map> getCode(String typeCode, String code, String type) {
        Map<String, String> params = buildParams(typeCode, code, type);
        return dao.query("tedcm01.query", params);
    }

    @Override
    public List<Map> getCode(String typeCode, String code) {
        return getCode(typeCode, code, null);
    }

    @Override
    public List<Map> getCode(String typeCode) {
        return getCode(typeCode, null, "1");
    }

    @Override
    public List<Map<String, String>> getCodeAndName(String typeCode) {
        return getCodeAndName(typeCode, null, null);
    }

    public List<Map<String, String>> getCodeAndName(String typeCode, String code, String type) {
        Map<String, String> params = buildParams(typeCode, code, type);
        @SuppressWarnings("unchecked")
        List<Map<String, String>> list = dao.query("tedcm01.queryCodeAndName", params);
        return list;
    }

    @Override
    public List<Map<String, String>> getCodeAndName_page(String typeCode, int offset, int limit) {
        Map<String, String> params = buildParams(typeCode, null, null);
        return dao.query("tedcm01.queryCodeAndName", params, offset, limit);
    }

    @Override
    public int getCodeAndName_count(String typeCode) {
        Map<String, String> params = buildParams(typeCode, null, null);
        return dao.count("tedcm01.queryCodeAndName", params);
    }

    @Override
    public List getCodeByCodeCName(String codeCNAME, String typeCode) {
        Map<String, Object> params = new HashMap<>();
        params.put("itemCname", codeCNAME);
        params.put("codesetCode", typeCode);
        return dao.query("tedcm01.query", params);
    }

    @Override
    public List getCodeByCodeEName(String codeENAME, String typeCode) {
        Map<String, Object> params = new HashMap<>();
        params.put("itemEname", codeENAME);
        params.put("codesetCode", typeCode);
        return dao.query("tedcm01.query", params);
    }
}
