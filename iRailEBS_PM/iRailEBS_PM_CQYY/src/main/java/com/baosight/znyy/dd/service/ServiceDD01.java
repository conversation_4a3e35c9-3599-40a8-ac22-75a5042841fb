package com.baosight.znyy.dd.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.map.MapUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;


import com.baosight.iplat4j.core.cache.CacheManager;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XEventManager;
import com.baosight.iplat4j.core.util.DateUtils;
import com.baosight.znyy.common.IPlatCodeInfo;
import com.baosight.znyy.common.util.BusUtil;
import com.baosight.znyy.common.util.ConvertUtil;
import com.baosight.znyy.common.util.JacksonUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class ServiceDD01 extends ServiceBase {

    @Resource
    IPlatCodeInfo iplatCodeInfo;

    private final Map<String, Object> planCacheManager = CacheManager.getCache("irailebs:znyy:dd:plan");
    private final Map<String, Object> eventCacheManager = CacheManager.getCache("irailebs:znyy:dd:event");

    /**
     * 根据事件描述推荐预案
     *
     * @param inInfo
     * @return {@link EiInfo }
     */
    public EiInfo planRecByEvent(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        // 参数获取
        Map<String, Object> paramMap = BusUtil.parse2Map(inInfo);
        String eventDesc = paramMap.getOrDefault("eventDesc", "").toString();
        if (eventDesc.isEmpty()) {
            inInfo.setMsg("事件描述不能为空");
            inInfo.set("status", EiConstant.STATUS_FAILURE);
            return inInfo;
        }
        // @TODO 检查EVNET_UUUID
        // 1.1插入事件列表
        try {
            inInfo.addRow("event", paramMap);
            super.insert(inInfo, "DD01.insertEvent", "event");
        } catch (Exception e) {
            log.error("插入事件列表失败:{}", e.getMessage(), e);
        }

        //1.2请求智能体
        EiInfo asyncInfo = new EiInfo();
        asyncInfo.set("eventDesc", eventDesc);
        asyncInfo.set(EiConstant.eventId, "E_CQYY_DD_0101");
        outInfo = XEventManager.callAsync(asyncInfo);
        if (outInfo.getStatus() < 0) {
            throw new PlatException(outInfo.getMsg());
        }
        return outInfo;
    }


    // 请求智能体
    public EiInfo requestRecommendation(EiInfo inInfo) {


//        curl --request POST \
//        --url http://************:30081/api/v2/openapi/chat \
//        --header 'Accept: */*' \
//        --header 'Accept-Encoding: gzip, deflate, br' \
//        --header 'Authorization: Bearer rAHXIYBeyA1BWuaE8csXyiHqgTQO9N6h' \
//        --header 'Connection: keep-alive' \
//        --header 'Content-Type: application/json' \
//        --header 'User-Agent: PostmanRuntime-ApipostRuntime/1.1.0' \
//        --data '{
//        "agent_id": 2250,
//                "session_id": "bb797c52-ce8f-4894-a788-4dab4571859d",
//                "user": "o3oxx",
//                "query": "【1号线】17:45，石井坡牵降所再生制动装置框架保护动作，210、410开关跳闸，再生制动及逆变系统退出运行。已通知相关部门处理。",
//                "stream": false,
//                "verbose": true,
//                "version": "20250605162917-v6"
//    }'
        // 请求智能体
        Map<String, String> headers = MapUtil.builder("Authorization", "Bearer rAHXIYBeyA1BWuaE8csXyiHqgTQO9N6h")
                .put("Content-Type", "application/json")
                .put("Accept", "*/*")
                .put("Accept-Encoding", "gzip, deflate, br")
                .put("Connection", "keep-alive")
                .put("User-Agent", "PostmanRuntime-ApipostRuntime/1.1.0")
                .build();

        Map<String, Object> body = MapUtil.builder(new HashMap<String, Object>())
                .put("agent_id", 2250)
                .put("session_id", "bb797c52-ce8f-4894-a788-4dab4571859d")
                .put("user", "o3oxx")
                .put("query", "【1号线】17:45，石井坡牵降所再生制动装置框架保护动作，210、410开关跳闸，再生制动及逆变系统退出运行。已通知相关部门处理。")
                .put("stream", false)
                .put("verbose", true)
                .put("version", "20250605162917-v6")
                .build();

//        String paramsStr = JSONUtil.parse(body).toStringPretty();

        try (
                HttpResponse response = HttpUtil.createPost("").addHeaders(headers).body("").execute()
        ) {
            System.out.println(response.charset("UTF-8").body());
        // 解析智能体数据

            // 小代码根据线路名称获取预案类型

            // 根据预案类型查询预案

            // 过滤预案

            // 存在 ->插入数据 （事件id,预案id） 更新缓存
        }  // 自动调用 close()
        return inInfo;
    }

    /**
     * 根据事件ID获取推荐预案
     *
     * @param inInfo
     * @return {@link EiInfo }
     */
    public EiInfo getRecsByEventId(EiInfo inInfo) {
        TimeInterval timer = DateUtil.timer();
        EiInfo outInfo = new EiInfo();
        String eventUUIDs = inInfo.getString("eventUUIDs");
        if (eventUUIDs == null || eventUUIDs.isEmpty()) {
            inInfo.setMsg("事件ID不能为空");
            inInfo.set("status", EiConstant.STATUS_FAILURE);
            return inInfo;
        }

        // 检查缓存
        if (planCacheManager.containsKey(eventUUIDs)) {
            try {
                List<Map<String, Object>> planRecList = JacksonUtils.jsonToList(planCacheManager.getOrDefault(eventUUIDs, "").toString());
                outInfo.set("data", planRecList);
                outInfo.set("status", EiConstant.STATUS_SUCCESS);
                outInfo.setMsg("查询推荐预案成功!");
                log.info("查询推荐预案成功，本次从缓存中获取!耗时:{} ms", timer.intervalRestart());
                return outInfo;
            } catch (IOException e) {
                log.error("缓存推荐预案解析失败:{}", e.getMessage(), e);
                planCacheManager.remove(eventUUIDs);
                log.info("缓存推荐预案解析失败，已删除缓存，尝试从数据库中获取!");
            }
        }
        // 从数据库中查询
        List<?> res = dao.query("DD01.getRecsByEventId", inInfo.getAttr(), 0, -999999);
        if (res.isEmpty()) {
            outInfo.setMsg("未查询到推荐预案!");
            outInfo.set("status", EiConstant.STATUS_DEFAULT);
            log.warn("未查询到推荐预案!");
            return outInfo;
        }
        log.info("查询推荐预案成功，本次从数据库中获取!耗时:{} ms", timer.intervalRestart());
        // 转换数据
        List<Map<String, Object>> planRecList = ConvertUtil.parseArray(res)
                .stream()
                .peek(m -> m.remove("eventUUIDs"))
                .collect(Collectors.toList());
        outInfo.set("data", planRecList);
        outInfo.set("status", EiConstant.STATUS_SUCCESS);
        outInfo.setMsg("查询推荐预案成功!");
        try {
            planCacheManager.put(eventUUIDs, new ObjectMapper().writeValueAsString(planRecList));
        } catch (IOException e) {
            log.error("缓存推荐预案失败:{}", e.getMessage(), e);
        }
        return outInfo;
    }


    /**
     * 记录实际选择的预案
     *
     * @param inInfo
     * @return {@link EiInfo }
     */
    public EiInfo recordPlanSelection(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        Map<String, Object> map = BusUtil.parse2Map(inInfo);
        map.put("createTime", DateUtils.curDateTimeStr14());
        inInfo.addRow("planRec", map);
        try {
            super.insert(inInfo, "DD01.recordPlanSelection", "planRec");
            outInfo.setMsg("记录成功");
            outInfo.setStatus(1);
            return outInfo;
        } catch (Exception e) {
            outInfo.setMsg(inInfo.getMsg());
            outInfo.set("status", EiConstant.STATUS_FAILURE);
            log.error(e.getMessage(), e);
            return outInfo;
        }
    }
}
