<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="DD01">

    <!-- 根据事件ID获取推荐预案 -->
    <select id="getRecsByEventId" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        <!--        SELECT-->
        <!--        FD_EVENT_ID as "eventUUIDs",-->
        <!--        FD_PLAN_ID_ITEM as "planUUIDs"-->
        <!--        FROM ${projectSchema}.T_RECOMMEND_PLAN-->
        <!--        WHERE FD_EVENT_ID=#eventUUIDs#-->
        WITH split_data AS (
        SELECT
        FD_EVENT_ID,
        TRIM(REGEXP_SUBSTR(FD_PLAN_ID_ITEM, '[^,]+', 1, LEVEL)) AS FD_PLAN_UUID
        FROM
        biz_cqyy.T_RECOMMEND_PLAN
        WHERE
        FD_EVENT_ID = #eventUUIDs#
        CONNECT BY
        PRIOR FD_EVENT_ID = FD_EVENT_ID AND
        PRIOR SYS_GUID() IS NOT NULL AND
        LEVEL &lt;= LENGTH(REGEXP_REPLACE(FD_PLAN_ID_ITEM, '[^,]', '')) + 1
        )
        SELECT
        s.FD_EVENT_ID as "eventUUIDs",
        s.FD_PLAN_UUID as "planUUIDs",
        b.FD_NAME as "planName"
        FROM
        split_data s
        JOIN
        biz_cqyy.T_PLAN_CURRENT b ON s.FD_PLAN_UUID = b.FD_PLAN_UUID
        ORDER BY
        s.FD_EVENT_ID, s.FD_PLAN_UUID
    </select>


    <!-- 插入实际选中预案反馈信息 -->
    <insert id="recordPlanSelection" parameterClass="java.util.HashMap">
        INSERT INTO ${projectSchema}.T_ACTUAL_SELECT_PLAN (
        FD_EVENT_ID, FD_PLAN_ID_ITEM, FD_CREATE_TIME )
        VALUES(#eventUUIDs#, #planUUIDs#, #createTime#)
    </insert>

    <!-- 查询预案信息 -->
    <select id="queryPlanInfo" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT
        FD_PLAN_UUID as "planUUIDs",
        FD_NAME as "planName"
        FROM ${projectSchema}.T_PLAN_CURRENT
        <dynamic prepend="WHERE">
            <isNotEmpty property="planUUIDs" prepend="and">
                FD_PLAN_UUID = #planUUIDs#
            </isNotEmpty>
            <isNotEmpty property="planType" prepend="and">
                FD_PLAN_T = #planType#
            </isNotEmpty>
            <isNotEmpty property="planName" prepend="and">
                FD_NAME = #planName#
            </isNotEmpty>
        </dynamic>
    </select>

    <!-- 插入事件信息 -->
    <insert id="insertEvent" parameterClass="java.util.HashMap">
        INSERT INTO ${projectSchema}.T_EVENT (
        FD_UUID, FD_NAME, FD_TIME, FD_AREA_INFO, FD_RESP_T, FD_DESC, FD_OPERATOR, FD_REPORT_T )
        VALUES(#eventUUIDs#, #eventName#, #eventTime#, #eventAddress#, #eventLevel#, #eventDesc#, #operator#,
        #reportType#)
    </insert>
</sqlMap>