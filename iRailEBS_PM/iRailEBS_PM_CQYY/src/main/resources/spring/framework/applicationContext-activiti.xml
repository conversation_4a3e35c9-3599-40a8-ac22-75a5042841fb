<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.0.xsd">

    <bean id="uuidGenerator" class="org.activiti.engine.impl.persistence.StrongUuidGenerator" />

    <bean id="processEngineConfiguration" class="org.activiti.spring.SpringProcessEngineConfiguration">
        <property name="dataSource" ref="dataSource" />
        <property name="databaseType" value="oracle" />
        <property name="transactionManager" ref="transactionManager" />
        <property name="databaseSchemaUpdate" value="true" />
        <property name="jobExecutorActivate" value="false" />
        <!--<property name="databaseSchema" value="ACT"/>-->
        <!--缓存避免每次都访问数据库-->
        <property name="processDefinitionCacheLimit" value="10" />
        <property name="idGenerator" ref="uuidGenerator"/>
        <property name="mailServerHost" value="*************" />
        <property name="mailServerPort" value="25" />
    </bean>

    <bean id="activitiProcessEngine" class="org.activiti.spring.ProcessEngineFactoryBean" destroy-method="destroy">
        <property name="processEngineConfiguration" ref="processEngineConfiguration" />
    </bean>



    <bean id="repositoryService" factory-bean="activitiProcessEngine" factory-method="getRepositoryService" />

    <bean id="runtimeService" factory-bean="activitiProcessEngine" factory-method="getRuntimeService" />

    <bean id="taskService" factory-bean="activitiProcessEngine" factory-method="getTaskService" />

    <bean id="historyService" factory-bean="activitiProcessEngine" factory-method="getHistoryService" />

    <bean id="managementService" factory-bean="activitiProcessEngine" factory-method="getManagementService" />

    <bean id="identityService" factory-bean="activitiProcessEngine" factory-method="getIdentityService" />

</beans>