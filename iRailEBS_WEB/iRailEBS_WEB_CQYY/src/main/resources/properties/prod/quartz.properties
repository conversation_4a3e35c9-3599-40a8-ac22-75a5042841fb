#???????????
xservices.job.startupDelay=60

org.quartz.scheduler.instanceName = iPlat4j_Scheduler
org.quartz.scheduler.instanceId = AUTO
org.quartz.threadPool.class = org.quartz.simpl.SimpleThreadPool
org.quartz.threadPool.threadCount = 20
org.quartz.threadPool.threadPriority = 5
org.quartz.jobStore.misfireThreshold = 60000
#????
#org.quartz.jobStore.class = org.quartz.simpl.RAMJobStore


###????????????
org.quartz.jobStore.class = org.quartz.impl.jdbcjobstore.JobStoreTX
org.quartz.jobStore.driverDelegateClass = org.quartz.impl.jdbcjobstore.StdJDBCDelegate
org.quartz.jobStore.useProperties = false
org.quartz.jobStore.dataSource = appDS
org.quartz.jobStore.tablePrefix = EJ_QRTZ_
###????????true
#org.quartz.jobStore.isClustered = true

##JNDI????
#org.quartz.dataSource.appDS.jndiURL=appDS

##????????????????????
org.quartz.jobStore.clusterCheckinInterval = 20000
org.quartz.dataSource.appDS.driver = dm.jdbc.driver.DmDriver
#????
org.quartz.dataSource.appDS.URL = jdbc:dm://************:5236?schema=iplat&columnNameUpperCase=false

org.quartz.dataSource.appDS.user = iplat
org.quartz.dataSource.appDS.password = Bx@123456
org.quartz.dataSource.appDS.maxConnections = 30


org.quartz.plugin.logging.class = com.baosight.xservices.ej.job.quartz.JobLoggingPlugin
org.quartz.plugin.logging.tablePrefix = EJ_QRTZ_

org.quartz.plugin.triggHistory.class = org.quartz.plugins.history.LoggingTriggerHistoryPlugin
