$(function () {

    window.onload = function () {
        // 获取 URL 参数，可以从配置或URL中动态获取
        const orderParam = '5,2,3,4,1';  
        const regId = "inqu";
        
        // 调用表单排序函数
        adjustFormGroupsOrder(orderParam, regId);

        // 禁用配置环境名称输入框
      //  disableKendoElement($("#inqu_status-0-configEnvCname"), true);
        
        // 禁用项目环境下拉框
        // disableKendoElement($("#inqu_status-0-projectEnv"), true);
        
     
    };

    /**
     * 调整表单组件的显示顺序
     * @param {string} orderParam - 顺序参数，如"5,2,3,4,1"
     * @param {string} regId - 表单区域的ID
     */
    function adjustFormGroupsOrder(orderParam, regId) {
        if (orderParam) {
            setTimeout(function() {
                try {
                    // 获取需要调整顺序的标签
                    const region = document.getElementById(regId);
                    if (!region) {
                        console.warn(`未找到ID为${regId}的区域`);
                        return;
                    }
                    
                    const formGroups = Array.from(region.querySelectorAll('.form-group'));
                    if (formGroups.length === 0) {
                        console.warn('未找到form-group元素');
                        return;
                    }
                    
                    // 获取每个formGroup的父节点作为容器
                    const containers = formGroups.map(formGroup => formGroup.parentNode);
                    if (containers.length === 0) {
                        console.warn('未找到容器元素');
                        return;
                    }
                    
                    // 解析顺序参数
                    // 表示原始位置的元素移动到目标位置
                    const orderArray = orderParam.split(',').map(num => parseInt(num, 10) - 1);
                    
                    // 创建一个数组来保存重新排序后的元素
                    const reorderedFormGroups = new Array(formGroups.length);
                    
                    // 将每个元素放到新的位置
                    for (let i = 0; i < formGroups.length && i < orderArray.length; i++) {
                        const newPosition = orderArray[i];
                        if (newPosition >= 0 && newPosition < reorderedFormGroups.length) {
                            reorderedFormGroups[newPosition] = formGroups[i];
                        } else {
                            console.warn(`无效的位置索引: ${newPosition}, 对应元素索引: ${i}`);
                        }
                    }
                    
                    // 检查是否所有位置都有元素
                    const missingPositions = reorderedFormGroups.findIndex(item => !item);
                    if (missingPositions !== -1) {
                        console.warn(`位置 ${missingPositions} 没有对应的元素`);
                    }
                    
                    // 确保容器数量足够
                    if (containers.length < reorderedFormGroups.length) {
                        console.warn(`容器数量(${containers.length})少于表单组数量(${reorderedFormGroups.length})`);
                    }
                    
                    // 先清空所有容器
                    containers.forEach(container => {
                        while (container.firstChild) {
                            container.removeChild(container.firstChild);
                        }
                    });
                    
                    // 将重排后的元素放回容器
                    for (let i = 0; i < reorderedFormGroups.length; i++) {
                        if (reorderedFormGroups[i]) {
                            // 确保有足够的容器
                            if (i < containers.length) {
                                containers[i].appendChild(reorderedFormGroups[i]);
                            } else {
                                // 如果容器不够，将剩余元素添加到最后一个容器
                                containers[containers.length - 1].appendChild(reorderedFormGroups[i]);
                                console.warn(`元素 ${i} 被添加到最后一个容器，因为容器数量不足`);
                            }
                        }
                    }
                    
                    console.log('表单排序完成');
                } catch (error) {
                    console.error('表单排序过程中发生错误:', error);
                }
            }, 100); // 短暂延迟确保DOM已完全加载
        }
    }

    var projectRows, module1Rows, module2Rows;

    $("#QUERY").on("click", function (e) {
        resultGrid.dataSource.page(1);
    });

    IPLATUI.EFGrid = {
        "result": {
           // reorderable:true,
            // 使用loadComplete事件处理grid列排序
            loadComplete: function(grid) {
                // 此grid对象
                // 处理父子级联动,通过监听change事件，判断父级节点是否发生变化
                grid.dataSource.bind("change", function (e) {
                    // 判断父级节点是否发生变化
                    var cell_label;
                    if (e.field == "project") {
                        cell_label = "moduleEname_1";
                    } else if (e.field == "moduleEname_1") {
                        cell_label = "moduleEname_2";
                    }
                    // cell_label 表示子级节点 ，that 表示此grid
                    var that = grid;

                    // locked 表示是否为固定列
                    var locked = that.isCellLocked(cell_label);
                    // tr 表示locked和非locked的行,index 表示此行的第几列
                    var tr, index;
                    // 获取此model元素信息
                    var item = e.items[0];
                    if (null != item) {
                        var _uid = item.uid;
                        if (locked) {
                            tr = $(".k-grid-content-locked tr[data-uid=" + _uid + "]");
                            index = $("th[data-field='" + cell_label + "']").data("index");
                        } else {
                            tr = $(".k-grid-content tr[data-uid=" + _uid + "]");
                            index = parseInt($("th[data-field='" + cell_label + "']").data("index")) - that.lockedHeader.find("th").length;
                        }
                        // 获取子cell(td)
                        var td = tr.children("td:eq(" + index + ")");
                        // 触发td.click事件，
                        td.trigger("click");
                    }
                });
                
                // 执行列排序
                try {
                    // 使用更直观的对象格式参数
                    const columnMoves = [
                        { from: 5, to: 9 },{from: 6, to:8},  // 将原第3列移动到第5列位置

                    ];

                    console.log('在EFGrid loadComplete中执行列排序');

                    // 自动检测第一列
                    reorderGridColumns(grid, columnMoves, true);
                } catch (error) {
                    console.error('在EFGrid loadComplete中执行列排序时发生错误:', error);
                }
                
                EiCommunicator.send("EDCC02", "getDropDownListData", new EiInfo(), {
                    onSuccess: function (ei) {
                        projectRows = ei.getBlock("project").getMappedRows();
                        module1Rows = ei.getBlock("moduleEname_1").getMappedRows();
                        module2Rows = ei.getBlock("moduleEname_2").getMappedRows();
                        var a = $("#inqu_status-0-project").data("kendoDropDownList");
                        a.setDataSource(projectRows);
                        a.setOptions({dataTextField: 'projectEname', dataValueField: 'projectEname'});
                        a.select(function (dataItem) {
                            return dataItem.projectEname === __eiInfo.get("project");
                        });
                    }
                });
            },
            columns: [
                {
                    // 定义父级column
                    field: "project",
                    title: "项目英文名",
                    validation: {required: true},
                    readonly: true,
                    locked: true,
                    // 重写editor方法，
                    editor: function (container, options) {
                        var input = $('<input />');
                        input.attr("name", options.field);
                        input.appendTo(container);

                        // 在grid 的 cell中创建下拉选择框
                        input.kendoDropDownList({
                            valuePrimitive: true,
                            dataTextField: "projectEname",
                            dataValueField: "projectEname",
                            dataSource: projectRows
                        }).appendTo(container);
                    }
                }, {
                    // 定义子级column
                    field: "moduleEname_1",
                    title: "一级模块英文名",
                    validation: {required: true},
                    readonly: true,
                    locked: true,
                    // 重写editor方法
                    editor: function (container, options) {
                        // container 表示正在编辑的的cell(td)
                        var tr = container.parent();

                        // resultGrid 是平台提供的一个全局的Grid对象，开发人员需要根据不同的Grid去获取不同的全局对象
                        // 这里定义的是result，所以可以使用resultGrid这个全局对象
                        // item 表示此行的model
                        var item = resultGrid.dataItem(tr);

                        // 获取此model的父级信息
                        // 这里是处理 "项目—>模块" 这种层级关系，所以"模块"对应的上级一级就是"项目"
                        var _project = item['project'];

                        // 获取__eiInfo中block为module的模块信息
                        // 过滤出来"项目"下面对应的"一级模块"信息
                        var _data = _filter(module1Rows, "projectEname", _project);

                        // 创建好Input框
                        var input = $('<input />');

                        // _data[0] 表示获取的第一个元素信息
                        // 修改model中moduleEname值
                        if (_data.length == 0) {
                            var data = new Object();
                            data.projectEname = _project;
                            data.moduleEname_1 = "";
                            _data.push(data);
                        }
                        input.attr("name", options.field);
                        input.attr("required", "true");
                        $(".k-edit-cell").css("overflow", "visible");
                        input.appendTo(container);
                        var global = [{moduleEname_1: "GLOBAL"}];
                        var _dropDownList = input.kendoDropDownList({
                            autoBind: false,
                            dataTextField: "moduleEname_1",
                            dataValueField: "moduleEname_1",
                            optionLabel: "请选择",
                            dataSource: global.concat(_data),
                            value: _data[0]['moduleEname_1'],
                            text: _data[0]['moduleEname_1']
                        }).data("kendoDropDownList");
                    }
                }, {
                    // 定义子级column
                    field: "moduleEname_2",
                    title: "二级模块英文名",
                    validation: {required: true},
                    readonly: true,
                    locked: true,
                    // 重写editor方法
                    editor: function (container, options) {
                        // container 表示正在编辑的的cell(td)
                        var tr = container.parent();

                        // resultGrid 是平台提供的一个全局的Grid对象，开发人员需要根据不同的Grid去获取不同的全局对象
                        // 这里定义的是result，所以可以使用resultGrid这个全局对象
                        // item 表示此行的model
                        var item = resultGrid.dataItem(tr);

                        // 获取此model的父级信息
                        var moduleEname_1 = item['moduleEname_1'];

                        // 获取__eiInfo中block为moduleEname_2的模块信息
                        // 过滤出来"一级模块"下面对应的"二级模块"信息
                        var _data = _filter(module2Rows, "moduleEname_1", moduleEname_1);

                        // 创建好Input框
                        var input = $('<input />');

                        // _data[0] 表示获取的第一个元素信息
                        // 修改model中moduleEname_2值
                        if (_data.length == 0) {
                            var data = new Object();
                            data.moduleEname_1 = moduleEname_1;
                            data.moduleEname_2 = "";
                            _data.push(data);
                        }
                        input.attr("name", options.field);
                        input.attr("required", "true");
                        $(".k-edit-cell").css("overflow", "visible");
                        input.appendTo(container);
                        var global = [{moduleEname_2: "GLOBAL"}];
                        var _dropDownList = input.kendoDropDownList({
                            autoBind: false,
                            dataTextField: "moduleEname_2",
                            dataValueField: "moduleEname_2",
                            optionLabel: "请选择",
                            dataSource: global.concat(_data),
                            value: _data[0]['moduleEname_2'],
                            text: _data[0]['moduleEname_2']
                        }).data("kendoDropDownList");
                    }
                }
            ],
            dataBound: function () {
                /*console.log("数据渲染结束");*/
            }
        }
    };

    // 新增：KendoUI Grid列排序函数
    /**
     * 根据指定的移动指令重新排列KendoUI Grid的列
     * @param {object} grid - KendoUI Grid实例
     * @param {Array} columnMoves - 列移动指令数组，格式：[{from: 3, to: 5}, {from: 6, to: 2}]，表示将第3列移到第5列位置，第6列移到第2列位置
     * @param {boolean} skipFirstColumn - 是否跳过第一列（当第一列为勾选框等特殊列时）
     * @returns {boolean} - 是否成功重排列
     */
    function reorderGridColumns(grid, columnMoves, skipFirstColumn) {
        if (!grid || !columnMoves || !Array.isArray(columnMoves)) {
            console.warn('参数无效: grid为空或columnMoves不是数组');
            return false;
        }
        
        console.log('开始重排列顺序，移动指令:', JSON.stringify(columnMoves), '跳过第一列:', skipFirstColumn);
        
        try {
            // 检查Grid初始化状态
            if (!grid.wrapper || !grid.wrapper.length || !grid.columns || grid.columns.length === 0) {
                console.warn('Grid尚未完全初始化，无法执行列排序');
                return false;
            }
            
            // 获取所有可见列(过滤掉隐藏列)
            const allColumns = grid.columns;
            const visibleColumns = allColumns.filter(col => {
                // 判断列是否可见 - 检查各种可能的隐藏标志
                return !(
                    col.hidden === true || 
                    col.visible === false || 
                    col.hidden === 'true' || 
                    col.visible === 'false' || 
                    (col.width !== undefined && parseInt(col.width) === 0)
                );
            });
            
            // 创建可见列到实际列的映射，用于正确处理源列索引和目标索引
            const visibleToActualMap = {};
            visibleColumns.forEach((col, visibleIndex) => {
                visibleToActualMap[visibleIndex] = allColumns.indexOf(col);
            });
            
            // 检查第一列是否为特殊列（勾选框等）
            const hasHeaderTemplateFirst = skipFirstColumn || 
                (visibleColumns[0] && (
                    visibleColumns[0].headerTemplate || 
                    visibleColumns[0].template && visibleColumns[0].width && visibleColumns[0].width < 50 ||
                    visibleColumns[0].selectable ||
                    visibleColumns[0].field === '_checkbox_selector' ||
                    visibleColumns[0].field === 'checkbox' ||
                    visibleColumns[0].title === '' || 
                    visibleColumns[0].title === ' '
                ));
                
            // 确定起始偏移量    
            const offset = hasHeaderTemplateFirst ? 1 : 0;
            
            // 获取和验证列索引是否有效（只考虑可见列）
            let validMoves = [];
            for (const move of columnMoves) {
                // 输入的索引是基于可见列的，需要考虑偏移量
                const sourceVisibleIndex = move.from - 1 + offset;
                const targetVisibleIndex = move.to - 1 + offset;
                
                // 检查可见索引是否有效
                if (sourceVisibleIndex >= offset && sourceVisibleIndex < visibleColumns.length && 
                    targetVisibleIndex >= offset && targetVisibleIndex < visibleColumns.length && 
                    sourceVisibleIndex !== targetVisibleIndex) {
                    
                    // 将可见索引转换为实际索引
                    const sourceActualIndex = visibleToActualMap[sourceVisibleIndex];
                    const targetActualIndex = visibleToActualMap[targetVisibleIndex];
                    
                    if (sourceActualIndex !== undefined && targetActualIndex !== undefined) {
                        validMoves.push({
                            sourceVisibleIndex,
                            targetVisibleIndex,
                            sourceActualIndex,
                            targetActualIndex,
                            from: move.from,
                            to: move.to
                        });
                    }
                }
            }
            
            // 如果没有有效的移动指令，提前返回
            if (validMoves.length === 0) {
                console.warn('没有有效的列移动指令');
                return false;
            }
            
            // 获取表头和表体容器
            const $wrapper = $(grid.wrapper);
            const $headers = $wrapper.find('.k-grid-header');
            const $contents = $wrapper.find('.k-grid-content');
            
            let movesSucceeded = 0;
            
            // 移动列的主要逻辑
            for (const move of validMoves) {
                // 获取源列信息 - 使用实际索引
                const sourceColumn = allColumns[move.sourceActualIndex];
                
                if (!sourceColumn || !sourceColumn.field) {
                    continue;
                }
                
                // 获取源列字段名称
                const sourceField = sourceColumn.field;
                
                try {
                    // 在所有表头容器中查找源列表头
                    let $sourceHeader = null;
                    let $headerContainer = null;
                    
                    $headers.each(function() {
                        const $header = $(this);
                        const $found = $header.find(`th[data-field="${sourceField}"]:visible`);
                        if ($found.length > 0) {
                            $sourceHeader = $found;
                            $headerContainer = $header;
                            return false; // 跳出each循环
                        }
                    });
                    
                    if (!$sourceHeader || $sourceHeader.length === 0) {
                        // 尝试使用索引查找
                        let allVisibleHeadersArr = [];
                        $headers.each(function() {
                            const $header = $(this);
                            $header.find('th:visible').each(function() {
                                allVisibleHeadersArr.push($(this));
                            });
                        });
                        
                        if (move.sourceVisibleIndex < allVisibleHeadersArr.length) {
                            $sourceHeader = allVisibleHeadersArr[move.sourceVisibleIndex];
                            $headerContainer = $sourceHeader.closest('.k-grid-header');
                        } else {
                            continue;
                        }
                    }
                    
                    // 尝试找到目标位置 - 只查找可见表头
                    let $targetHeader = null;
                    const $allVisibleHeaders = $headerContainer.find('th:visible');
                    const visibleTargetIndex = Math.min(move.targetVisibleIndex, $allVisibleHeaders.length - 1);
                    $targetHeader = $allVisibleHeaders.eq(visibleTargetIndex);
                    
                    if (!$targetHeader || $targetHeader.length === 0) {
                        continue;
                    }
                    
                    // 移动表头单元格
                    if (move.sourceVisibleIndex < move.targetVisibleIndex) {
                        $sourceHeader.insertAfter($targetHeader);
                    } else {
                        $sourceHeader.insertBefore($targetHeader);
                    }
                    
                    // 在所有表体容器中查找并移动数据单元格
                    $contents.each(function() {
                        const $content = $(this);
                        const $rows = $content.find('tr');
                        
                        // 如果找不到任何行，跳过此容器
                        if ($rows.length === 0) return;
                        
                        $rows.each(function() {
                            const $row = $(this);
                            // 只选择可见单元格
                            const $cells = $row.find('td:visible');
                            
                            // 尝试通过字段名查找源单元格 (必须是可见的)
                            let $sourceCell = $cells.filter(`[data-field="${sourceField}"]`);
                            
                            // 如果找不到，尝试使用可见索引
                            if ($sourceCell.length === 0 && move.sourceVisibleIndex < $cells.length) {
                                $sourceCell = $cells.eq(move.sourceVisibleIndex);
                            }
                            
                            // 如果仍找不到，跳过此行
                            if ($sourceCell.length === 0) return;
                            
                            // 找到目标单元格位置 (基于可见单元格)
                            const visibleCellTargetIndex = Math.min(move.targetVisibleIndex, $cells.length - 1);
                            const $targetCell = $cells.eq(visibleCellTargetIndex);
                            
                            // 如果找不到目标单元格，跳过此行
                            if ($targetCell.length === 0) return;
                            
                            // 移动单元格
                            if (move.sourceVisibleIndex < move.targetVisibleIndex) {
                                $sourceCell.insertAfter($targetCell);
                            } else {
                                $sourceCell.insertBefore($targetCell);
                            }
                        });
                    });
                    
                    // 更新Grid内部列数组，避免排序、过滤等操作出错
                    // 这里需要使用实际索引，而不是可见索引
                    const columnsCopy = $.extend(true, [], allColumns);
                    const sourceColumnCopy = columnsCopy[move.sourceActualIndex];
                    
                    // 移动列在内部数组中的位置
                    if (move.sourceActualIndex < move.targetActualIndex) {
                        // 向右移动
                        for (let i = move.sourceActualIndex; i < move.targetActualIndex; i++) {
                            columnsCopy[i] = columnsCopy[i + 1];
                        }
                    } else {
                        // 向左移动
                        for (let i = move.sourceActualIndex; i > move.targetActualIndex; i--) {
                            columnsCopy[i] = columnsCopy[i - 1];
                        }
                    }
                    
                    // 将源列放到目标位置
                    columnsCopy[move.targetActualIndex] = sourceColumnCopy;
                    
                    // 直接更新Grid的columns属性
                    grid.columns = columnsCopy;
                    
                    movesSucceeded++;
                    console.log(`移动列: 第${move.from}列 -> 第${move.to}列`);
                } catch (moveError) {
                    console.error(`移动列出错:`, moveError);
                }
            }
            
            // 触发最小刷新，更新内部状态但不重绘整个表格
            try {
                // 仅更新布局，不重新绑定数据
                if (typeof grid.resize === 'function') {
                    grid.resize();
                }
                
                // 手动触发一次columnReorder事件，确保内部状态更新
                if (typeof grid.trigger === 'function') {
                    grid.trigger("columnReorder");
                }
                
                if (movesSucceeded > 0) {
                    console.log(`列排序完成，成功移动${movesSucceeded}列`);
                    return true;
                } else {
                    console.warn('没有成功移动任何列');
                    return false;
                }
            } catch (refreshError) {
                if (movesSucceeded > 0) {
                    console.warn('刷新布局出错，但列移动已完成');
                    return true;
                }
                return false;
            }
        } catch (error) {
            console.error('列排序出错:', error);
            return false;
        }
    }

    function _filter(arr, field, value) {
        if ($.isArray(arr)) {
            return arr.filter(function (item) {
                return item[field] == value
            });
        }
        return [];
    }

    /**
     * 通用的KendoUI元素禁用函数，根据DOM元素的ID自动识别控件类型并应用相应的禁用方法
     * @param {string|jQuery} element - 元素的ID选择器字符串或jQuery对象
     * @param {boolean} disable - 是否禁用，true表示禁用，false表示启用
     * @returns {boolean} - 操作是否成功
     */
    function disableKendoElement(element, disable) {
        // 确保有一个jQuery对象
        const $element = typeof element === 'string' ? $(element) : element;
        
        if (!$element || $element.length === 0) {
            console.warn('未找到元素:', element);
            return false;
        }
        
        try {
            // 尝试获取不同类型的Kendo控件实例
            const kendoWidgets = [
                { name: "kendoDropDownList", method: "enable" },
                { name: "kendoComboBox", method: "enable" },
                { name: "kendoMultiSelect", method: "enable" },
                { name: "kendoTreeView", method: "enable" },
                { name: "kendoGrid", method: "enable" },
                { name: "kendoDatePicker", method: "enable" },
                { name: "kendoDateTimePicker", method: "enable" },
                { name: "kendoTimePicker", method: "enable" },
                { name: "kendoNumericTextBox", method: "enable" },
                { name: "kendoMaskedTextBox", method: "enable" },
                { name: "kendoAutoComplete", method: "enable" },
                { name: "kendoSlider", method: "enable" },
                { name: "kendoSwitch", method: "enable" },
                { name: "kendoUpload", method: "enable" },
                { name: "kendoEditor", method: "enable" },
                { name: "kendoColorPicker", method: "enable" },
                { name: "kendoButton", method: "enable" }
            ];
            
            // 检查每种类型的Kendo控件
            for (const widget of kendoWidgets) {
                const instance = $element.data(widget.name);
                if (instance && typeof instance[widget.method] === 'function') {
                    instance[widget.method](!disable); // enable方法接受相反的布尔值
                    console.log(`禁用${disable ? '禁用' : '启用'}Kendo控件:`, widget.name, element);
                    return true;
                }
            }
            
            // 如果找不到Kendo控件，尝试使用标准DOM属性禁用
            $element.prop('disabled', disable);
            
            // 对于非Kendo的输入类控件，添加或移除disabled类
            if (disable) {
                $element.addClass('k-state-disabled');
            } else {
                $element.removeClass('k-state-disabled');
            }
            
            console.log(`使用标准方式${disable ? '禁用' : '启用'}元素:`, element);
            return true;
        } catch (error) {
            console.error(`${disable ? '禁用' : '启用'}元素时出错:`, element, error);
            return false;
        }
    }
});
